#!/usr/bin/env python3
"""
Test script to verify RAG application setup
"""

import sys
import requests
from pathlib import Path

def test_imports():
    """Test if all modules can be imported"""
    print("🔍 Testing module imports...")
    
    try:
        import config
        print("✅ config module")
        
        from src.pdf_processor import PDFProcessor
        print("✅ PDF processor")
        
        from src.embeddings import OllamaEmbeddings
        print("✅ Ollama embeddings")
        
        from src.vector_store import ChromaVectorStore
        print("✅ Vector store")
        
        from src.retrieval_qa import RAGChatbot
        print("✅ RAG chatbot")
        
        from src.utils import check_ollama_status
        print("✅ Utilities")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_ollama_connection():
    """Test Ollama connection"""
    print("\n🔍 Testing Ollama connection...")
    
    try:
        from src.utils import check_ollama_status
        status = check_ollama_status()
        
        if status['status'] == 'running':
            print("✅ Ollama is running")
            print(f"   Available models: {len(status.get('available_models', []))}")
            
            # Check specific models
            models = status.get('available_models', [])
            if 'llama3.1:latest' in models:
                print("✅ llama3.1:latest model available")
            else:
                print("❌ llama3.1:latest model missing")
                print("   Run: ollama pull llama3.1:latest")
            
            if 'nomic-embed-text' in models:
                print("✅ nomic-embed-text model available")
            else:
                print("❌ nomic-embed-text model missing")
                print("   Run: ollama pull nomic-embed-text")
            
            return True
        else:
            print(f"❌ Ollama not available: {status.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Ollama: {e}")
        return False

def test_embeddings():
    """Test embedding functionality"""
    print("\n🔍 Testing embeddings...")
    
    try:
        from src.embeddings import EmbeddingManager
        
        manager = EmbeddingManager()
        success = manager.test_embedding("This is a test sentence.")
        
        if success:
            print("✅ Embeddings working correctly")
            dimension = manager.get_embedding_dimension()
            if dimension:
                print(f"   Embedding dimension: {dimension}")
            return True
        else:
            print("❌ Embeddings test failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing embeddings: {e}")
        return False

def test_vector_store():
    """Test vector store initialization"""
    print("\n🔍 Testing vector store...")
    
    try:
        from src.vector_store import create_vector_store
        
        vector_store = create_vector_store()
        info = vector_store.get_collection_info()
        
        print("✅ Vector store initialized")
        print(f"   Collection: {info.get('collection_name', 'Unknown')}")
        print(f"   Document count: {info.get('document_count', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing vector store: {e}")
        return False

def test_pdf_processor():
    """Test PDF processor"""
    print("\n🔍 Testing PDF processor...")
    
    try:
        from src.pdf_processor import PDFProcessor
        
        processor = PDFProcessor()
        print("✅ PDF processor initialized")
        print(f"   Chunk size: {processor.chunk_size}")
        print(f"   Chunk overlap: {processor.chunk_overlap}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing PDF processor: {e}")
        return False

def test_directories():
    """Test if required directories exist"""
    print("\n🔍 Testing directories...")
    
    directories = ["data/uploads", "chroma_db", "src"]
    
    for directory in directories:
        if Path(directory).exists():
            print(f"✅ {directory}")
        else:
            print(f"❌ {directory} missing")
            return False
    
    return True

def test_files():
    """Test if required files exist"""
    print("\n🔍 Testing files...")
    
    files = [
        "requirements.txt",
        "config.py", 
        "app.py",
        "src/__init__.py",
        "src/pdf_processor.py",
        "src/embeddings.py",
        "src/vector_store.py",
        "src/retrieval_qa.py",
        "src/utils.py"
    ]
    
    for file_path in files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} missing")
            return False
    
    return True

def main():
    """Main test function"""
    print("🧪 RAG Application - Setup Test")
    print("=" * 40)
    
    tests = [
        ("Files", test_files),
        ("Directories", test_directories),
        ("Imports", test_imports),
        ("PDF Processor", test_pdf_processor),
        ("Ollama Connection", test_ollama_connection),
        ("Embeddings", test_embeddings),
        ("Vector Store", test_vector_store),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} test failed")
        except Exception as e:
            print(f"❌ {test_name} test error: {e}")
    
    print("\n" + "=" * 40)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your setup is ready.")
        print("\nTo start the application:")
        print("  streamlit run app.py")
    else:
        print("⚠️  Some tests failed. Please check the setup.")
        if passed < 3:  # Critical failures
            print("❌ Critical setup issues detected.")
            print("Please run: python setup.py")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
