"""
Vector Store Module for RAG Application
Handles Chroma vector database operations
"""

import logging
from pathlib import Path
from typing import List, Optional, Dict, Any
import chromadb
from chromadb.config import Settings
from langchain.vectorstores import Chroma
from langchain.schema import Document
from src.embeddings import OllamaEmbeddings
import config

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ChromaVectorStore:
    """
    Manages Chroma vector store operations for the RAG application
    """
    
    def __init__(self, 
                 persist_directory: str = str(config.CHROMA_DB_DIR),
                 collection_name: str = config.COLLECTION_NAME,
                 embedding_model: str = config.OLLAMA_EMBEDDING_MODEL):
        """
        Initialize Chroma vector store
        
        Args:
            persist_directory: Directory to persist the database
            collection_name: Name of the collection
            embedding_model: Ollama model for embeddings
        """
        self.persist_directory = Path(persist_directory)
        self.collection_name = collection_name
        self.embedding_model = embedding_model
        
        # Create persist directory if it doesn't exist
        self.persist_directory.mkdir(parents=True, exist_ok=True)
        
        # Initialize embeddings
        self.embeddings = OllamaEmbeddings(model=embedding_model)
        
        # Initialize Chroma client
        self.client = chromadb.PersistentClient(
            path=str(self.persist_directory),
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )
        
        # Initialize vector store
        self.vector_store = None
        self._initialize_vector_store()
    
    def _initialize_vector_store(self):
        """Initialize the Chroma vector store"""
        try:
            self.vector_store = Chroma(
                client=self.client,
                collection_name=self.collection_name,
                embedding_function=self.embeddings,
                persist_directory=str(self.persist_directory)
            )
            logger.info(f"Initialized Chroma vector store at {self.persist_directory}")
            
        except Exception as e:
            logger.error(f"Failed to initialize vector store: {e}")
            raise
    
    def add_documents(self, documents: List[Document]) -> List[str]:
        """
        Add documents to the vector store
        
        Args:
            documents: List of Document objects to add
            
        Returns:
            List of document IDs
        """
        try:
            if not documents:
                logger.warning("No documents provided to add")
                return []
            
            logger.info(f"Adding {len(documents)} documents to vector store")
            
            # Add documents to vector store
            ids = self.vector_store.add_documents(documents)
            
            # Persist the changes
            self.vector_store.persist()
            
            logger.info(f"Successfully added {len(ids)} documents to vector store")
            return ids
            
        except Exception as e:
            logger.error(f"Error adding documents to vector store: {e}")
            raise
    
    def similarity_search(self, 
                         query: str, 
                         k: int = config.SIMILARITY_SEARCH_K,
                         filter_dict: Optional[Dict[str, Any]] = None) -> List[Document]:
        """
        Perform similarity search
        
        Args:
            query: Search query
            k: Number of results to return
            filter_dict: Optional metadata filter
            
        Returns:
            List of similar documents
        """
        try:
            if filter_dict:
                results = self.vector_store.similarity_search(
                    query, k=k, filter=filter_dict
                )
            else:
                results = self.vector_store.similarity_search(query, k=k)
            
            logger.debug(f"Found {len(results)} similar documents for query")
            return results
            
        except Exception as e:
            logger.error(f"Error in similarity search: {e}")
            return []
    
    def similarity_search_with_score(self, 
                                   query: str, 
                                   k: int = config.SIMILARITY_SEARCH_K) -> List[tuple]:
        """
        Perform similarity search with relevance scores
        
        Args:
            query: Search query
            k: Number of results to return
            
        Returns:
            List of (document, score) tuples
        """
        try:
            results = self.vector_store.similarity_search_with_score(query, k=k)
            logger.debug(f"Found {len(results)} similar documents with scores")
            return results
            
        except Exception as e:
            logger.error(f"Error in similarity search with score: {e}")
            return []
    
    def get_collection_info(self) -> Dict[str, Any]:
        """
        Get information about the collection
        
        Returns:
            Dictionary with collection information
        """
        try:
            collection = self.client.get_collection(self.collection_name)
            count = collection.count()
            
            return {
                "collection_name": self.collection_name,
                "document_count": count,
                "persist_directory": str(self.persist_directory)
            }
            
        except Exception as e:
            logger.error(f"Error getting collection info: {e}")
            return {}
    
    def delete_collection(self):
        """Delete the entire collection"""
        try:
            self.client.delete_collection(self.collection_name)
            logger.info(f"Deleted collection: {self.collection_name}")
            
            # Reinitialize vector store
            self._initialize_vector_store()
            
        except Exception as e:
            logger.error(f"Error deleting collection: {e}")
            raise
    
    def update_documents(self, documents: List[Document], ids: List[str]):
        """
        Update existing documents in the vector store
        
        Args:
            documents: List of updated Document objects
            ids: List of document IDs to update
        """
        try:
            if len(documents) != len(ids):
                raise ValueError("Number of documents must match number of IDs")
            
            # Delete existing documents
            self.vector_store.delete(ids)
            
            # Add updated documents
            self.vector_store.add_documents(documents, ids=ids)
            
            # Persist changes
            self.vector_store.persist()
            
            logger.info(f"Updated {len(documents)} documents in vector store")
            
        except Exception as e:
            logger.error(f"Error updating documents: {e}")
            raise
    
    def search_by_metadata(self, 
                          metadata_filter: Dict[str, Any], 
                          limit: int = 10) -> List[Document]:
        """
        Search documents by metadata
        
        Args:
            metadata_filter: Metadata filter criteria
            limit: Maximum number of results
            
        Returns:
            List of matching documents
        """
        try:
            # Use a dummy query since we're filtering by metadata
            results = self.vector_store.similarity_search(
                query="", 
                k=limit, 
                filter=metadata_filter
            )
            
            logger.debug(f"Found {len(results)} documents matching metadata filter")
            return results
            
        except Exception as e:
            logger.error(f"Error searching by metadata: {e}")
            return []
    
    def get_retriever(self, 
                     search_type: str = "similarity",
                     search_kwargs: Optional[Dict[str, Any]] = None):
        """
        Get a retriever for the vector store
        
        Args:
            search_type: Type of search ("similarity", "mmr", etc.)
            search_kwargs: Additional search parameters
            
        Returns:
            Retriever object
        """
        try:
            if search_kwargs is None:
                search_kwargs = {"k": config.SIMILARITY_SEARCH_K}
            
            retriever = self.vector_store.as_retriever(
                search_type=search_type,
                search_kwargs=search_kwargs
            )
            
            logger.debug(f"Created retriever with search_type: {search_type}")
            return retriever
            
        except Exception as e:
            logger.error(f"Error creating retriever: {e}")
            raise


def create_vector_store(embedding_model: str = config.OLLAMA_EMBEDDING_MODEL) -> ChromaVectorStore:
    """
    Factory function to create a vector store instance
    
    Args:
        embedding_model: Ollama model for embeddings
        
    Returns:
        ChromaVectorStore instance
    """
    return ChromaVectorStore(embedding_model=embedding_model)
