"""
Utility functions for RAG Application
"""

import logging
import os
import shutil
from pathlib import Path
from typing import List, Dict, Any, Optional
import streamlit as st
import requests
import config

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def setup_logging(log_level: str = "INFO"):
    """
    Setup logging configuration
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
    """
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f'Invalid log level: {log_level}')
    
    logging.basicConfig(
        level=numeric_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('rag_app.log')
        ]
    )


def check_ollama_status() -> Dict[str, Any]:
    """
    Check if Ollama is running and available
    
    Returns:
        Dictionary with status information
    """
    try:
        response = requests.get(f"{config.OLLAMA_BASE_URL}/api/tags", timeout=5)
        
        if response.status_code == 200:
            models = response.json().get('models', [])
            model_names = [model['name'] for model in models]
            
            return {
                "status": "running",
                "available_models": model_names,
                "text_model_available": config.OLLAMA_MODEL in model_names,
                "embedding_model_available": config.OLLAMA_EMBEDDING_MODEL in model_names,
                "base_url": config.OLLAMA_BASE_URL
            }
        else:
            return {
                "status": "error",
                "error": f"HTTP {response.status_code}",
                "base_url": config.OLLAMA_BASE_URL
            }
            
    except requests.exceptions.ConnectionError:
        return {
            "status": "not_running",
            "error": "Cannot connect to Ollama",
            "base_url": config.OLLAMA_BASE_URL
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "base_url": config.OLLAMA_BASE_URL
        }


def format_file_size(size_bytes: int) -> str:
    """
    Format file size in human readable format
    
    Args:
        size_bytes: Size in bytes
        
    Returns:
        Formatted size string
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def validate_uploaded_file(uploaded_file) -> Dict[str, Any]:
    """
    Validate uploaded file
    
    Args:
        uploaded_file: Streamlit uploaded file object
        
    Returns:
        Dictionary with validation results
    """
    if uploaded_file is None:
        return {"valid": False, "error": "No file uploaded"}
    
    # Check file extension
    file_extension = Path(uploaded_file.name).suffix.lower()
    if file_extension not in config.ALLOWED_EXTENSIONS:
        return {
            "valid": False, 
            "error": f"Invalid file type. Allowed: {', '.join(config.ALLOWED_EXTENSIONS)}"
        }
    
    # Check file size
    file_size_mb = uploaded_file.size / (1024 * 1024)
    if file_size_mb > config.MAX_FILE_SIZE_MB:
        return {
            "valid": False,
            "error": f"File too large. Maximum size: {config.MAX_FILE_SIZE_MB} MB"
        }
    
    return {
        "valid": True,
        "name": uploaded_file.name,
        "size": uploaded_file.size,
        "size_formatted": format_file_size(uploaded_file.size),
        "type": uploaded_file.type
    }


def save_uploaded_file(uploaded_file, upload_dir: Path = config.DATA_DIR) -> Path:
    """
    Save uploaded file to disk
    
    Args:
        uploaded_file: Streamlit uploaded file object
        upload_dir: Directory to save the file
        
    Returns:
        Path to saved file
    """
    try:
        upload_dir.mkdir(parents=True, exist_ok=True)
        file_path = upload_dir / uploaded_file.name
        
        with open(file_path, "wb") as f:
            f.write(uploaded_file.getbuffer())
        
        logger.info(f"Saved uploaded file: {file_path}")
        return file_path
        
    except Exception as e:
        logger.error(f"Error saving uploaded file: {e}")
        raise


def clean_upload_directory(upload_dir: Path = config.DATA_DIR, keep_recent: int = 10):
    """
    Clean up old files in upload directory
    
    Args:
        upload_dir: Directory to clean
        keep_recent: Number of recent files to keep
    """
    try:
        if not upload_dir.exists():
            return
        
        # Get all PDF files sorted by modification time
        pdf_files = list(upload_dir.glob("*.pdf"))
        pdf_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        # Remove old files
        files_to_remove = pdf_files[keep_recent:]
        for file_path in files_to_remove:
            file_path.unlink()
            logger.info(f"Removed old file: {file_path}")
        
        if files_to_remove:
            logger.info(f"Cleaned up {len(files_to_remove)} old files")
            
    except Exception as e:
        logger.error(f"Error cleaning upload directory: {e}")


def get_system_info() -> Dict[str, Any]:
    """
    Get system information for debugging
    
    Returns:
        Dictionary with system information
    """
    try:
        import platform
        import psutil
        
        return {
            "platform": platform.platform(),
            "python_version": platform.python_version(),
            "cpu_count": psutil.cpu_count(),
            "memory_total": format_file_size(psutil.virtual_memory().total),
            "memory_available": format_file_size(psutil.virtual_memory().available),
            "disk_usage": format_file_size(psutil.disk_usage('/').free)
        }
    except ImportError:
        return {"error": "psutil not available for system info"}
    except Exception as e:
        return {"error": str(e)}


def create_download_link(file_path: Path, link_text: str = "Download") -> str:
    """
    Create a download link for a file
    
    Args:
        file_path: Path to the file
        link_text: Text for the download link
        
    Returns:
        HTML download link
    """
    try:
        with open(file_path, "rb") as f:
            file_data = f.read()
        
        import base64
        b64_data = base64.b64encode(file_data).decode()
        
        href = f'<a href="data:application/octet-stream;base64,{b64_data}" download="{file_path.name}">{link_text}</a>'
        return href
        
    except Exception as e:
        logger.error(f"Error creating download link: {e}")
        return f"Error: {str(e)}"


def display_error_message(error: Exception, context: str = ""):
    """
    Display formatted error message in Streamlit
    
    Args:
        error: Exception object
        context: Additional context information
    """
    error_msg = f"**Error in {context}:** {str(error)}" if context else f"**Error:** {str(error)}"
    st.error(error_msg)
    
    with st.expander("Error Details"):
        st.code(str(error))
        if hasattr(error, '__traceback__'):
            import traceback
            st.code(traceback.format_exc())


def display_success_message(message: str, details: Optional[str] = None):
    """
    Display formatted success message in Streamlit
    
    Args:
        message: Success message
        details: Optional additional details
    """
    st.success(message)
    
    if details:
        with st.expander("Details"):
            st.info(details)


@st.cache_data
def load_css_style() -> str:
    """
    Load custom CSS styles for the application
    
    Returns:
        CSS style string
    """
    return """
    <style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .chat-message {
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    
    .user-message {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
    }
    
    .assistant-message {
        background-color: #f3e5f5;
        border-left: 4px solid #9c27b0;
    }
    
    .source-document {
        background-color: #f5f5f5;
        padding: 0.5rem;
        border-radius: 0.25rem;
        margin: 0.25rem 0;
        font-size: 0.9rem;
    }
    
    .status-indicator {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 5px;
    }
    
    .status-running {
        background-color: #4caf50;
    }
    
    .status-error {
        background-color: #f44336;
    }
    </style>
    """
