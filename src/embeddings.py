"""
Embeddings Module for RAG Application
Handles Ollama embeddings generation and management
"""

import logging
import requests
import json
from typing import List, Optional
import numpy as np
from langchain.embeddings.base import Embeddings
import config

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class OllamaEmbeddings(Embeddings):
    """
    Custom Ollama embeddings class compatible with LangChain
    """
    
    def __init__(self, 
                 model: str = config.OLLAMA_EMBEDDING_MODEL,
                 base_url: str = config.OLLAMA_BASE_URL):
        """
        Initialize Ollama embeddings
        
        Args:
            model: Ollama model name for embeddings
            base_url: Base URL for Ollama API
        """
        self.model = model
        self.base_url = base_url
        self.embed_url = f"{base_url}/api/embeddings"
        
        # Test connection
        self._test_connection()
    
    def _test_connection(self) -> bool:
        """
        Test connection to Ollama server
        
        Returns:
            True if connection successful, raises exception otherwise
        """
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=10)
            if response.status_code == 200:
                models = response.json().get('models', [])
                model_names = [model['name'] for model in models]
                
                if self.model not in model_names:
                    logger.warning(f"Model {self.model} not found. Available models: {model_names}")
                    logger.info(f"You may need to pull the model: ollama pull {self.model}")
                
                logger.info(f"Successfully connected to Ollama at {self.base_url}")
                return True
            else:
                raise ConnectionError(f"Failed to connect to Ollama: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Cannot connect to Ollama at {self.base_url}: {e}")
            logger.info("Make sure Ollama is running: ollama serve")
            raise
    
    def _get_embedding(self, text: str) -> List[float]:
        """
        Get embedding for a single text
        
        Args:
            text: Text to embed
            
        Returns:
            Embedding vector as list of floats
        """
        try:
            payload = {
                "model": self.model,
                "prompt": text
            }
            
            response = requests.post(
                self.embed_url,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                embedding = result.get('embedding')
                
                if embedding is None:
                    raise ValueError("No embedding returned from Ollama")
                
                return embedding
            else:
                error_msg = f"Ollama API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                raise RuntimeError(error_msg)
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Request error when getting embedding: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error when getting embedding: {e}")
            raise
    
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        Embed multiple documents
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embedding vectors
        """
        embeddings = []
        
        for i, text in enumerate(texts):
            try:
                if i % 10 == 0:  # Log progress every 10 documents
                    logger.info(f"Embedding document {i+1}/{len(texts)}")
                
                embedding = self._get_embedding(text)
                embeddings.append(embedding)
                
            except Exception as e:
                logger.error(f"Failed to embed document {i}: {e}")
                # Use zero vector as fallback
                if embeddings:
                    zero_embedding = [0.0] * len(embeddings[0])
                else:
                    zero_embedding = [0.0] * 384  # Default dimension
                embeddings.append(zero_embedding)
        
        logger.info(f"Successfully embedded {len(embeddings)} documents")
        return embeddings
    
    def embed_query(self, text: str) -> List[float]:
        """
        Embed a single query text
        
        Args:
            text: Query text to embed
            
        Returns:
            Embedding vector as list of floats
        """
        try:
            embedding = self._get_embedding(text)
            logger.debug(f"Successfully embedded query of length {len(text)}")
            return embedding
            
        except Exception as e:
            logger.error(f"Failed to embed query: {e}")
            raise


class EmbeddingManager:
    """
    Manager class for handling embeddings operations
    """
    
    def __init__(self, model: str = config.OLLAMA_EMBEDDING_MODEL):
        """
        Initialize embedding manager
        
        Args:
            model: Ollama model name for embeddings
        """
        self.model = model
        self.embeddings = OllamaEmbeddings(model=model)
    
    def get_embeddings_instance(self) -> OllamaEmbeddings:
        """
        Get the embeddings instance
        
        Returns:
            OllamaEmbeddings instance
        """
        return self.embeddings
    
    def test_embedding(self, test_text: str = "This is a test sentence.") -> bool:
        """
        Test embedding functionality
        
        Args:
            test_text: Text to use for testing
            
        Returns:
            True if test successful, False otherwise
        """
        try:
            embedding = self.embeddings.embed_query(test_text)
            
            if embedding and len(embedding) > 0:
                logger.info(f"Embedding test successful. Dimension: {len(embedding)}")
                return True
            else:
                logger.error("Embedding test failed: empty embedding returned")
                return False
                
        except Exception as e:
            logger.error(f"Embedding test failed: {e}")
            return False
    
    def get_embedding_dimension(self) -> Optional[int]:
        """
        Get the dimension of embeddings from this model
        
        Returns:
            Embedding dimension or None if cannot determine
        """
        try:
            test_embedding = self.embeddings.embed_query("test")
            return len(test_embedding) if test_embedding else None
            
        except Exception as e:
            logger.error(f"Cannot determine embedding dimension: {e}")
            return None


def check_ollama_models() -> List[str]:
    """
    Check available models in Ollama
    
    Returns:
        List of available model names
    """
    try:
        response = requests.get(f"{config.OLLAMA_BASE_URL}/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json().get('models', [])
            return [model['name'] for model in models]
        else:
            logger.error(f"Failed to get models: {response.status_code}")
            return []
            
    except Exception as e:
        logger.error(f"Error checking Ollama models: {e}")
        return []
