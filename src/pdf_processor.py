"""
PDF Processing Module for RAG Application
Handles PDF loading, text extraction, and chunking
"""

import logging
from pathlib import Path
from typing import List, Optional
import PyPDF2
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document
import config

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PDFProcessor:
    """
    Handles PDF file processing including text extraction and chunking
    """
    
    def __init__(self, chunk_size: int = config.CHUNK_SIZE, 
                 chunk_overlap: int = config.CHUNK_OVERLAP):
        """
        Initialize PDF processor with chunking parameters
        
        Args:
            chunk_size: Size of text chunks
            chunk_overlap: Overlap between chunks
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
            separators=["\n\n", "\n", " ", ""]
        )
    
    def extract_text_from_pdf(self, pdf_path: Path) -> str:
        """
        Extract text from a PDF file
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            Extracted text as string
        """
        try:
            text = ""
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text:
                            text += f"\n--- Page {page_num + 1} ---\n"
                            text += page_text
                    except Exception as e:
                        logger.warning(f"Error extracting text from page {page_num + 1}: {e}")
                        continue
                        
            if not text.strip():
                raise ValueError("No text could be extracted from the PDF")
                
            logger.info(f"Successfully extracted {len(text)} characters from {pdf_path.name}")
            return text
            
        except Exception as e:
            logger.error(f"Error processing PDF {pdf_path}: {e}")
            raise
    
    def chunk_text(self, text: str, metadata: Optional[dict] = None) -> List[Document]:
        """
        Split text into chunks using LangChain text splitter
        
        Args:
            text: Text to be chunked
            metadata: Optional metadata to attach to chunks
            
        Returns:
            List of Document objects with chunked text
        """
        try:
            # Split text into chunks
            chunks = self.text_splitter.split_text(text)
            
            # Create Document objects with metadata
            documents = []
            for i, chunk in enumerate(chunks):
                doc_metadata = metadata.copy() if metadata else {}
                doc_metadata.update({
                    "chunk_id": i,
                    "chunk_size": len(chunk),
                    "total_chunks": len(chunks)
                })
                
                documents.append(Document(
                    page_content=chunk,
                    metadata=doc_metadata
                ))
            
            logger.info(f"Created {len(documents)} chunks from text")
            return documents
            
        except Exception as e:
            logger.error(f"Error chunking text: {e}")
            raise
    
    def process_pdf(self, pdf_path: Path) -> List[Document]:
        """
        Complete PDF processing pipeline: extract text and create chunks
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            List of Document objects with chunked text
        """
        try:
            # Extract text from PDF
            text = self.extract_text_from_pdf(pdf_path)
            
            # Create metadata
            metadata = {
                "source": str(pdf_path),
                "filename": pdf_path.name,
                "file_size": pdf_path.stat().st_size,
                "total_characters": len(text)
            }
            
            # Chunk the text
            documents = self.chunk_text(text, metadata)
            
            logger.info(f"Successfully processed {pdf_path.name} into {len(documents)} chunks")
            return documents
            
        except Exception as e:
            logger.error(f"Error in PDF processing pipeline for {pdf_path}: {e}")
            raise
    
    def process_multiple_pdfs(self, pdf_paths: List[Path]) -> List[Document]:
        """
        Process multiple PDF files
        
        Args:
            pdf_paths: List of paths to PDF files
            
        Returns:
            Combined list of Document objects from all PDFs
        """
        all_documents = []
        
        for pdf_path in pdf_paths:
            try:
                documents = self.process_pdf(pdf_path)
                all_documents.extend(documents)
            except Exception as e:
                logger.error(f"Failed to process {pdf_path}: {e}")
                continue
        
        logger.info(f"Processed {len(pdf_paths)} PDFs into {len(all_documents)} total chunks")
        return all_documents


def validate_pdf_file(file_path: Path) -> bool:
    """
    Validate if a file is a valid PDF
    
    Args:
        file_path: Path to the file
        
    Returns:
        True if valid PDF, False otherwise
    """
    try:
        if not file_path.exists():
            return False
            
        if file_path.suffix.lower() != '.pdf':
            return False
            
        # Try to open with PyPDF2 to validate
        with open(file_path, 'rb') as file:
            PyPDF2.PdfReader(file)
            
        return True
        
    except Exception:
        return False
