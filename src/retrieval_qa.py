"""
Retrieval QA Module for RAG Application
Handles question-answering with conversation memory using LangChain
"""

import logging
from typing import Dict, List, Any, Optional
import requests
import json
from langchain.llms.base import LLM
from langchain.chains import ConversationalR<PERSON>rie<PERSON><PERSON>hain
from langchain.memory import ConversationBufferWindowMemory
from langchain.schema import BaseMessage, HumanMessage, AIMessage
from langchain.callbacks.manager import CallbackManagerForLLMRun
from src.vector_store import ChromaVectorStore
import config

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class OllamaLLM(LLM):
    """
    Custom Ollama LLM class compatible with LangChain
    """
    
    model: str = config.OLLAMA_MODEL
    base_url: str = config.OLLAMA_BASE_URL
    temperature: float = 0.7
    max_tokens: int = config.MAX_TOKENS
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.generate_url = f"{self.base_url}/api/generate"
        self._test_connection()
    
    def _test_connection(self):
        """Test connection to Ollama server"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=10)
            if response.status_code == 200:
                models = response.json().get('models', [])
                model_names = [model['name'] for model in models]
                
                if self.model not in model_names:
                    logger.warning(f"Model {self.model} not found. Available models: {model_names}")
                    logger.info(f"You may need to pull the model: ollama pull {self.model}")
                
                logger.info(f"Successfully connected to Ollama with model {self.model}")
            else:
                raise ConnectionError(f"Failed to connect to Ollama: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Cannot connect to Ollama at {self.base_url}: {e}")
            logger.info("Make sure Ollama is running: ollama serve")
            raise
    
    @property
    def _llm_type(self) -> str:
        return "ollama"
    
    def _call(self, 
              prompt: str, 
              stop: Optional[List[str]] = None,
              run_manager: Optional[CallbackManagerForLLMRun] = None,
              **kwargs: Any) -> str:
        """
        Call the Ollama model
        
        Args:
            prompt: Input prompt
            stop: Stop sequences
            run_manager: Callback manager
            **kwargs: Additional arguments
            
        Returns:
            Generated text
        """
        try:
            payload = {
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": self.temperature,
                    "num_predict": self.max_tokens
                }
            }
            
            if stop:
                payload["stop"] = stop
            
            response = requests.post(
                self.generate_url,
                json=payload,
                timeout=120  # Increased timeout for longer responses
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get('response', '')
            else:
                error_msg = f"Ollama API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                raise RuntimeError(error_msg)
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Request error when calling Ollama: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error when calling Ollama: {e}")
            raise


class RAGChatbot:
    """
    RAG-based chatbot with conversation memory
    """
    
    def __init__(self, 
                 vector_store: ChromaVectorStore,
                 model: str = config.OLLAMA_MODEL,
                 memory_length: int = config.MAX_MEMORY_LENGTH):
        """
        Initialize RAG chatbot
        
        Args:
            vector_store: ChromaVectorStore instance
            model: Ollama model name
            memory_length: Number of conversation turns to remember
        """
        self.vector_store = vector_store
        self.model = model
        self.memory_length = memory_length
        
        # Initialize LLM
        self.llm = OllamaLLM(model=model)
        
        # Initialize memory
        self.memory = ConversationBufferWindowMemory(
            k=memory_length,
            memory_key=config.MEMORY_KEY,
            return_messages=True,
            output_key='answer'
        )
        
        # Initialize retrieval chain
        self._initialize_chain()
    
    def _initialize_chain(self):
        """Initialize the conversational retrieval chain"""
        try:
            # Get retriever from vector store
            retriever = self.vector_store.get_retriever(
                search_kwargs={"k": config.SIMILARITY_SEARCH_K}
            )
            
            # Create conversational retrieval chain
            self.qa_chain = ConversationalRetrievalChain.from_llm(
                llm=self.llm,
                retriever=retriever,
                memory=self.memory,
                return_source_documents=True,
                verbose=True
            )
            
            logger.info("Successfully initialized conversational retrieval chain")
            
        except Exception as e:
            logger.error(f"Error initializing retrieval chain: {e}")
            raise
    
    def chat(self, question: str) -> Dict[str, Any]:
        """
        Process a chat question and return response with sources
        
        Args:
            question: User question
            
        Returns:
            Dictionary with answer, sources, and metadata
        """
        try:
            logger.info(f"Processing question: {question[:100]}...")
            
            # Get response from chain
            result = self.qa_chain({"question": question})
            
            # Extract information
            answer = result.get('answer', '')
            source_documents = result.get('source_documents', [])
            
            # Format sources
            sources = []
            for doc in source_documents:
                source_info = {
                    "content": doc.page_content[:200] + "..." if len(doc.page_content) > 200 else doc.page_content,
                    "metadata": doc.metadata
                }
                sources.append(source_info)
            
            response = {
                "answer": answer,
                "sources": sources,
                "question": question,
                "num_sources": len(sources)
            }
            
            logger.info(f"Generated response with {len(sources)} sources")
            return response
            
        except Exception as e:
            logger.error(f"Error processing chat question: {e}")
            return {
                "answer": f"I apologize, but I encountered an error while processing your question: {str(e)}",
                "sources": [],
                "question": question,
                "num_sources": 0
            }
    
    def get_conversation_history(self) -> List[Dict[str, str]]:
        """
        Get the conversation history
        
        Returns:
            List of conversation turns
        """
        try:
            messages = self.memory.chat_memory.messages
            history = []
            
            for message in messages:
                if isinstance(message, HumanMessage):
                    history.append({"type": "human", "content": message.content})
                elif isinstance(message, AIMessage):
                    history.append({"type": "ai", "content": message.content})
            
            return history
            
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            return []
    
    def clear_memory(self):
        """Clear the conversation memory"""
        try:
            self.memory.clear()
            logger.info("Cleared conversation memory")
            
        except Exception as e:
            logger.error(f"Error clearing memory: {e}")
    
    def summarize_documents(self, max_docs: int = 10) -> str:
        """
        Generate a summary of the documents in the vector store
        
        Args:
            max_docs: Maximum number of documents to include in summary
            
        Returns:
            Summary text
        """
        try:
            # Get some sample documents
            sample_query = "summary overview content"
            docs = self.vector_store.similarity_search(sample_query, k=max_docs)
            
            if not docs:
                return "No documents found in the knowledge base."
            
            # Create summary prompt
            doc_contents = []
            for i, doc in enumerate(docs):
                content = doc.page_content[:300] + "..." if len(doc.page_content) > 300 else doc.page_content
                doc_contents.append(f"Document {i+1}: {content}")
            
            combined_content = "\n\n".join(doc_contents)
            
            summary_prompt = f"""
            Please provide a comprehensive summary of the following documents. 
            Focus on the main topics, key points, and overall themes:

            {combined_content}

            Summary:
            """
            
            # Generate summary
            summary = self.llm._call(summary_prompt)
            
            logger.info("Generated document summary")
            return summary
            
        except Exception as e:
            logger.error(f"Error generating summary: {e}")
            return f"Error generating summary: {str(e)}"


def create_rag_chatbot(vector_store: ChromaVectorStore, 
                      model: str = config.OLLAMA_MODEL) -> RAGChatbot:
    """
    Factory function to create a RAG chatbot instance
    
    Args:
        vector_store: ChromaVectorStore instance
        model: Ollama model name
        
    Returns:
        RAGChatbot instance
    """
    return RAGChatbot(vector_store, model)
