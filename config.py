"""
Configuration file for RAG application with Ollama
"""
import os
from pathlib import Path

# Base directories
BASE_DIR = Path(__file__).parent
DATA_DIR = BASE_DIR / "data" / "uploads"
CHROMA_DB_DIR = BASE_DIR / "chroma_db"

# Create directories if they don't exist
DATA_DIR.mkdir(parents=True, exist_ok=True)
CHROMA_DB_DIR.mkdir(parents=True, exist_ok=True)

# Ollama Configuration
OLLAMA_BASE_URL = "http://localhost:11434"  # Default Ollama URL
OLLAMA_MODEL = "llama3.1:latest"  # Model for text generation
OLLAMA_EMBEDDING_MODEL = "nomic-embed-text"  # Model for embeddings

# Text Processing Configuration
CHUNK_SIZE = 1000  # Size of text chunks for processing
CHUNK_OVERLAP = 200  # Overlap between chunks
MAX_TOKENS = 4000  # Maximum tokens for context

# Vector Store Configuration
COLLECTION_NAME = "pdf_documents"
SIMILARITY_SEARCH_K = 4  # Number of similar documents to retrieve

# Streamlit Configuration
PAGE_TITLE = "RAG Chat with Ollama"
PAGE_ICON = "📚"
LAYOUT = "wide"

# File Upload Configuration
ALLOWED_EXTENSIONS = [".pdf"]
MAX_FILE_SIZE_MB = 50

# API Keys and Sensitive Data (Leave blank - to be filled by user)
# Add any additional API keys here if needed for future enhancements
OPENAI_API_KEY = ""  # Placeholder for potential OpenAI integration
HUGGINGFACE_API_KEY = ""  # Placeholder for potential HuggingFace integration

# Conversation Memory Configuration
MEMORY_KEY = "chat_history"
MAX_MEMORY_LENGTH = 10  # Maximum number of conversation turns to remember

# UI Configuration
SIDEBAR_WIDTH = 300
CHAT_HEIGHT = 400
