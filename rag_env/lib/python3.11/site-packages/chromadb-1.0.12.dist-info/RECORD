../../../bin/chroma,sha256=vgVNKArH_zKD-M8gyU4GwJ7oYaC9VyGWL6WBu3zRX58,285
chromadb-1.0.12.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
chromadb-1.0.12.dist-info/METADATA,sha256=HGbE_palBoShkyh-mrw7MonqXec9il4l_7DakTlLd2Q,6914
chromadb-1.0.12.dist-info/RECORD,,
chromadb-1.0.12.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb-1.0.12.dist-info/WHEEL,sha256=6kcRj6OxKF73Kk9A-y1ed3Vm1nD_hs5oAkJ02tSEJJE,102
chromadb-1.0.12.dist-info/entry_points.txt,sha256=vb__WRXGM9B5tQ1Ysrcp3LIgagWxYfyNsd0NS8C_08U,46
chromadb-1.0.12.dist-info/licenses/LICENSE,sha256=xx0jnfkXJvxRnG63LTGOxlggYnIysveWIZ6H3PNdCrQ,11357
chromadb/__init__.py,sha256=uIyb51tbnFte5Pv2OJnNBLMWcPWbwbKfeyDWEUUlRck,12613
chromadb/__pycache__/__init__.cpython-311.pyc,,
chromadb/__pycache__/app.cpython-311.pyc,,
chromadb/__pycache__/base_types.cpython-311.pyc,,
chromadb/__pycache__/config.cpython-311.pyc,,
chromadb/__pycache__/errors.cpython-311.pyc,,
chromadb/__pycache__/serde.cpython-311.pyc,,
chromadb/__pycache__/types.cpython-311.pyc,,
chromadb/api/__init__.py,sha256=DlQb1DDNPK8Cc0nphFGXQoY_bmmjuoUzSHWshexsNXI,23106
chromadb/api/__pycache__/__init__.cpython-311.pyc,,
chromadb/api/__pycache__/async_api.cpython-311.pyc,,
chromadb/api/__pycache__/async_client.cpython-311.pyc,,
chromadb/api/__pycache__/async_fastapi.cpython-311.pyc,,
chromadb/api/__pycache__/base_http_client.cpython-311.pyc,,
chromadb/api/__pycache__/client.cpython-311.pyc,,
chromadb/api/__pycache__/collection_configuration.cpython-311.pyc,,
chromadb/api/__pycache__/configuration.cpython-311.pyc,,
chromadb/api/__pycache__/fastapi.cpython-311.pyc,,
chromadb/api/__pycache__/rust.cpython-311.pyc,,
chromadb/api/__pycache__/segment.cpython-311.pyc,,
chromadb/api/__pycache__/shared_system_client.cpython-311.pyc,,
chromadb/api/__pycache__/types.cpython-311.pyc,,
chromadb/api/async_api.py,sha256=Ft_opQ973nYWvugE3s22udyLlUz1lUf5sDUaLleU3Oo,23228
chromadb/api/async_client.py,sha256=jkAGDcyEW0hl3GkrEQ5escMJ2x5_mSRL7_OVb2DWYD4,16687
chromadb/api/async_fastapi.py,sha256=uR9uJ13GnRt5j6HA9YB68LILManCM5AC1rpYtvrDans,22586
chromadb/api/base_http_client.py,sha256=zjEz4j7iuHn5bD-KPStCXP7yEOqCxJjUjfFdi7HBUeI,3690
chromadb/api/client.py,sha256=x4lsm2ZXz2UGl8EuarcgYcnGXaD4aZvu_dfTZxpifQ4,16230
chromadb/api/collection_configuration.py,sha256=6w15lNAvVby8aH9y6KzxfXI4tJzeZYy5EPRNFyqh8tQ,26795
chromadb/api/configuration.py,sha256=2ltgx7IlvTl6i2du23yMl0f-YviY9Jib9xd7Rz4n-80,15173
chromadb/api/fastapi.py,sha256=lAgH0Aihw-1wXWA3KFsFO7kk4c-byKx72xXPr0U64fk,21831
chromadb/api/models/AsyncCollection.py,sha256=AwKKOUHcaPNG5JSSmGObReJpS2_6o8D5tLQng1NjW3A,15313
chromadb/api/models/Collection.py,sha256=x-kBtMUbS44Cdpfpd99ofC1m4BqfK8ZiOoN6x05gL_c,15293
chromadb/api/models/CollectionCommon.py,sha256=GAZUZY8U7V6Cql-Z8iQFO6Cbs93xEfn-9DdrLrm5BUE,18725
chromadb/api/models/__pycache__/AsyncCollection.cpython-311.pyc,,
chromadb/api/models/__pycache__/Collection.cpython-311.pyc,,
chromadb/api/models/__pycache__/CollectionCommon.cpython-311.pyc,,
chromadb/api/rust.py,sha256=ObxRZEv2CL2cto_dWwRj2poNztORu22P4RPiBf1mhXA,18701
chromadb/api/segment.py,sha256=WX-H29XarlGaTK1fYCAtQurjExBuEDUiQ7jyeXkGDUs,34420
chromadb/api/shared_system_client.py,sha256=mZQUgu-gDDUUTq4cOLwHHK5uhf519U5D-gDjSALJ7G0,3499
chromadb/api/types.py,sha256=TuqX6vazuDL139VJnQgw6L9k8-q3UVtj737zZYE0cRc,33267
chromadb/app.py,sha256=GzDY70YzAUqrWgqG9kxD4ItB4OkynjiGIEGchk0hhNA,168
chromadb/auth/__init__.py,sha256=-sAzhNhFu_4wA7uCbkO6-n5kVN9MFo1b1DxS9KO2oeg,8151
chromadb/auth/__pycache__/__init__.cpython-311.pyc,,
chromadb/auth/basic_authn/__init__.py,sha256=1PZm8LDsYuhUZMLFFC-9vQNy-wGb5WmHiuH6OExBNDg,5092
chromadb/auth/basic_authn/__pycache__/__init__.cpython-311.pyc,,
chromadb/auth/simple_rbac_authz/__init__.py,sha256=LdWxxv5EKF7hnX-3NEBF7RldDTmBSBVqP1ImW8dCBFI,2643
chromadb/auth/simple_rbac_authz/__pycache__/__init__.cpython-311.pyc,,
chromadb/auth/token_authn/__init__.py,sha256=FT3S8kcvF9Lp_hVvpMyMsHqJ1KrYDw2K6fkcFAE9qxM,8328
chromadb/auth/token_authn/__pycache__/__init__.cpython-311.pyc,,
chromadb/auth/utils/__init__.py,sha256=NxedfP8yj6vH2QyUURd8RztG4N_e3ssxG6_nxNCTTQo,3409
chromadb/auth/utils/__pycache__/__init__.cpython-311.pyc,,
chromadb/base_types.py,sha256=pBbgTQOUN-2rsTBIHGvCpeaE4ICPiINCG8K_5BMlJLM,1258
chromadb/chromadb_rust_bindings.pyi,sha256=6b9niNO2Esg09JGV60g6ckzlfyffGH03VeNBHvTN70Q,5741
chromadb/cli/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/cli/__pycache__/__init__.cpython-311.pyc,,
chromadb/cli/__pycache__/cli.cpython-311.pyc,,
chromadb/cli/__pycache__/utils.cpython-311.pyc,,
chromadb/cli/cli.py,sha256=oAXupaJMOfPhq9ttZ5vQ0EzkwKP7oT-iYhjK8wlJn94,1494
chromadb/cli/utils.py,sha256=W11wgM0emYAkvrNlKVWfG-a7z47Ds_7CAS9F5Vm5Ss0,1247
chromadb/config.py,sha256=5AjpCwD9FDUmzPnfAFU78pBOxzCZs7jprHLL0_XCUk4,18495
chromadb/db/__init__.py,sha256=BD8rjF_qJdNYq3J8T7KDuDu6Rw9gbanx9ZM5Hw0B7s8,3040
chromadb/db/__pycache__/__init__.cpython-311.pyc,,
chromadb/db/__pycache__/base.cpython-311.pyc,,
chromadb/db/__pycache__/migrations.cpython-311.pyc,,
chromadb/db/__pycache__/system.cpython-311.pyc,,
chromadb/db/base.py,sha256=E3PkE2CxmFMPs7PQyhOV5XmRwkTPpGHHv7UigpgeBw0,5765
chromadb/db/impl/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/db/impl/__pycache__/__init__.cpython-311.pyc,,
chromadb/db/impl/__pycache__/sqlite.cpython-311.pyc,,
chromadb/db/impl/__pycache__/sqlite_pool.cpython-311.pyc,,
chromadb/db/impl/grpc/__pycache__/client.cpython-311.pyc,,
chromadb/db/impl/grpc/__pycache__/server.cpython-311.pyc,,
chromadb/db/impl/grpc/client.py,sha256=n_QYeFXQSgoe1g-xCO7sHZOnZUp2UZk9czykqZJRKwU,20253
chromadb/db/impl/grpc/server.py,sha256=QtxAKffqNvBrb0MkJMvm9N2Gg4axLbov4hSP3TrcavQ,20693
chromadb/db/impl/sqlite.py,sha256=fCs0wBaznOqMxnM2WVeRWeau-ajFW5fSqTXjK5UrhAs,9427
chromadb/db/impl/sqlite_pool.py,sha256=Y1p5aWF-rT1poxAhfFWx3vYvrS6iLOxJpvp0LYQnLoI,5372
chromadb/db/migrations.py,sha256=HC2pYXYkuXxjb-mHRxqciD8XmknkJQ9MUbRuIE3pGnY,9630
chromadb/db/mixins/__pycache__/embeddings_queue.cpython-311.pyc,,
chromadb/db/mixins/__pycache__/sysdb.cpython-311.pyc,,
chromadb/db/mixins/embeddings_queue.py,sha256=ZiCIqKzSKvmNA9NqbUt8-CfCYniTY_nBjn_77Vvp2k8,19325
chromadb/db/mixins/sysdb.py,sha256=XiSiq8JnIAqQlU0f_z97TUrnAh4ClnttJP_XlScojUg,36583
chromadb/db/system.py,sha256=X1l990O_Ywap8jvJw-1b_BgwWI8IaKaUMOyhTpviUb4,6135
chromadb/errors.py,sha256=x4t7XqgT7tTk8KQ7UPhhYvFHue2YePbKEc6F7CZF8XE,4018
chromadb/execution/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/execution/__pycache__/__init__.cpython-311.pyc,,
chromadb/execution/executor/__pycache__/abstract.cpython-311.pyc,,
chromadb/execution/executor/__pycache__/distributed.cpython-311.pyc,,
chromadb/execution/executor/__pycache__/local.cpython-311.pyc,,
chromadb/execution/executor/abstract.py,sha256=thw-jwNX1F2iuwYh3bJFrDxtcBe-5aiGdmwdmPFbK2o,470
chromadb/execution/executor/distributed.py,sha256=XtATaxzDeo3nBhl11L6J1byenWiBA_QaNpMNQaoYDIA,9035
chromadb/execution/executor/local.py,sha256=xijuCr4ozvMjX2T0WP9CQwss9AfthcSF7QQD2IJb-zg,7518
chromadb/execution/expression/__pycache__/operator.cpython-311.pyc,,
chromadb/execution/expression/__pycache__/plan.cpython-311.pyc,,
chromadb/execution/expression/operator.py,sha256=BRLBhL9xs4ul1CPMbpAnRgCP47CQ7vToBQcepueB9QM,1439
chromadb/execution/expression/plan.py,sha256=Mj1JiwOnw0eYP8CI-R-3-XwQdyIV4KJa6jrJkyA98w0,550
chromadb/experimental/density_relevance.ipynb,sha256=GS6nIb7E3xPBdrFCgOrsID8H5TYi9zWiSDQ2J2aZHWU,368700
chromadb/ingest/__init__.py,sha256=s12KV1krkUByBz9CPYzjFkhsfUm1pF1Num0oc9cGfVA,4299
chromadb/ingest/__pycache__/__init__.cpython-311.pyc,,
chromadb/ingest/impl/__pycache__/utils.cpython-311.pyc,,
chromadb/ingest/impl/utils.py,sha256=4gSi24qYNWg-eCgesB3U_Nqx0lZ9gwyTJVvm9RngulA,1833
chromadb/log_config.yml,sha256=kK6gRC-h8KsuBHca4Hs_qPl_IM0CvZKOHoqKSAogVG8,921
chromadb/logservice/__pycache__/logservice.cpython-311.pyc,,
chromadb/logservice/logservice.py,sha256=R0HZESWBR2FYSo_cwwBVXhvtkE3LUrjEFeNQQxkYW_o,5870
chromadb/migrations/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/migrations/__pycache__/__init__.cpython-311.pyc,,
chromadb/migrations/embeddings_queue/00001-embeddings.sqlite.sql,sha256=-8vaxiHGvevlYa4L0-rIQHN6-CL07cDVZ7lOIGbuzyg,261
chromadb/migrations/embeddings_queue/00002-embeddings-queue-config.sqlite.sql,sha256=eI_ptAVSaCG-L04wXaOJFySHGFmi6o9IwfgYJ0Vlj9k,95
chromadb/migrations/metadb/00001-embedding-metadata.sqlite.sql,sha256=DnR35ivUCDDyj5mG8HA1l4F4d1AwRDJO_oyezIU5KEU,600
chromadb/migrations/metadb/00002-embedding-metadata.sqlite.sql,sha256=M33nC7icQr8BdHZC--kxD4zjhjWrUPLOgu6YrGZOdZs,334
chromadb/migrations/metadb/00003-full-text-tokenize.sqlite.sql,sha256=E_1YOYI8jx4jZDHC9xagrvAsW_2Aw1wVljSWkk6o3aQ,236
chromadb/migrations/metadb/00004-metadata-indices.sqlite.sql,sha256=Ruh0o7-ZrpVhH9j3gbmuzMyJedxOBskehqa1baw3hEM,387
chromadb/migrations/metadb/00005-max-seq-id-int.sqlite.sql,sha256=Nj9Eto_NpgniOeHiMmPWVaBDmkkDlNo9aQJ5k5pxbkU,1449
chromadb/migrations/sysdb/00001-collections.sqlite.sql,sha256=36VyD7KQiAyaerrJJiAkzTLacDoxJHG11k6ylhOgVb0,355
chromadb/migrations/sysdb/00002-segments.sqlite.sql,sha256=5Fmn9mj5yaOFYukiEf7szEVJ1aBKyilyOXKRSW1WShw,385
chromadb/migrations/sysdb/00003-collection-dimension.sqlite.sql,sha256=Rlyd1HO_CE5qzdz9FcJYfsrzJbMEOvSarsh-QAa96PM,54
chromadb/migrations/sysdb/00004-tenants-databases.sqlite.sql,sha256=DaWJfr1xpy7lQMEBEmVnH1726yHanqqh5YQI2KBIDcM,1228
chromadb/migrations/sysdb/00005-remove-topic.sqlite.sql,sha256=YKNIAxxLz5jWEcpjYvqLdUzIVEY7plJYqrNmMnojc0M,152
chromadb/migrations/sysdb/00006-collection-segment-metadata.sqlite.sql,sha256=V2jbfUn1VZINrMcBLxc8_dLrTD9xr5ENP9ZTZhEFppQ,396
chromadb/migrations/sysdb/00007-collection-config.sqlite.sql,sha256=54e3NnBvFz6bJ5UF4OcCf1eicDHf52aKFnZ3AwcV1bk,106
chromadb/migrations/sysdb/00008-maintenance-log.sqlite.sql,sha256=Zq0x7v3dMHC-J5Y6BTMZ8P6QA-XdHzi1DELi7ZcIEq0,248
chromadb/migrations/sysdb/00009-segment-collection-not-null.sqlite.sql,sha256=tVZHgcKyTf3HkCSi6PF62YMBJ8ybQ4dtlCh1fAk7yZ4,327
chromadb/proto/.gitignore,sha256=Wume2NBKC5jrnYg5sumCElYcfjJPIGighn_j16FW-20,24
chromadb/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/proto/__pycache__/__init__.cpython-311.pyc,,
chromadb/proto/__pycache__/convert.cpython-311.pyc,,
chromadb/proto/__pycache__/utils.cpython-311.pyc,,
chromadb/proto/convert.py,sha256=xbb6lEUq5p2ArUv4nwkSRXzShWcpStaxo-u1sgMvV7A,26615
chromadb/proto/utils.py,sha256=RW9kRNsfcLsfBDXWpwwQOYbQwcIOLxs8JvT-_MTHIos,2570
chromadb/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/quota/__init__.py,sha256=RdSxuq8Glr9MKmLKZ4HfO-JsPJ9qxdm6xVoPxMIKblw,1817
chromadb/quota/__pycache__/__init__.cpython-311.pyc,,
chromadb/quota/simple_quota_enforcer/__init__.py,sha256=Am7THF60mexQGLvTpSBsYOfzNhvEe4M4WlU437Hzge8,1495
chromadb/quota/simple_quota_enforcer/__pycache__/__init__.cpython-311.pyc,,
chromadb/rate_limit/__init__.py,sha256=JKMziPCZkR3E3XbkmBTa4mYoMiH1ywmRVOgz_1w_0_c,889
chromadb/rate_limit/__pycache__/__init__.cpython-311.pyc,,
chromadb/rate_limit/simple_rate_limit/__init__.py,sha256=0sB4IBfdzOQzKGMBzKkVJDCrCy5emkETYDJH10t0R3E,1177
chromadb/rate_limit/simple_rate_limit/__pycache__/__init__.cpython-311.pyc,,
chromadb/segment/__init__.py,sha256=9fDMrjiU0vYROiochEOfyZk7fVUw7LbBZ0cQbVKMlZw,4020
chromadb/segment/__pycache__/__init__.cpython-311.pyc,,
chromadb/segment/distributed/__init__.py,sha256=BZmYYCIa8UymryJ_GDqD3sd48i-ucq_VNtwnXsKRbK8,2703
chromadb/segment/distributed/__pycache__/__init__.cpython-311.pyc,,
chromadb/segment/impl/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/segment/impl/__pycache__/__init__.cpython-311.pyc,,
chromadb/segment/impl/distributed/__pycache__/segment_directory.cpython-311.pyc,,
chromadb/segment/impl/distributed/segment_directory.py,sha256=s4sY3uSW5dLrN41Wm1-LMIRlUQjRsm0WLT28njvewH8,13393
chromadb/segment/impl/manager/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/segment/impl/manager/__pycache__/__init__.cpython-311.pyc,,
chromadb/segment/impl/manager/__pycache__/distributed.cpython-311.pyc,,
chromadb/segment/impl/manager/__pycache__/local.cpython-311.pyc,,
chromadb/segment/impl/manager/cache/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/segment/impl/manager/cache/__pycache__/__init__.cpython-311.pyc,,
chromadb/segment/impl/manager/cache/__pycache__/cache.cpython-311.pyc,,
chromadb/segment/impl/manager/cache/cache.py,sha256=KrMx8XtFDVMJZZCNUuYHZrTwjVTqvX5mgRE2UUSQIAs,3435
chromadb/segment/impl/manager/distributed.py,sha256=i6dDGdehvEe32h04XAPIoB8KCypuj3GgZBe2STv6bDQ,2986
chromadb/segment/impl/manager/local.py,sha256=S7TvReanT41cP5XcXa7yFmLojFbe85y3Vp_B4p3y_rU,10593
chromadb/segment/impl/metadata/__pycache__/sqlite.cpython-311.pyc,,
chromadb/segment/impl/metadata/sqlite.py,sha256=lzlhuLg7r2qRqE_wR7-OXDP16F7ayPMw4rLRcCdUIkk,25786
chromadb/segment/impl/vector/__pycache__/batch.cpython-311.pyc,,
chromadb/segment/impl/vector/__pycache__/brute_force_index.cpython-311.pyc,,
chromadb/segment/impl/vector/__pycache__/hnsw_params.cpython-311.pyc,,
chromadb/segment/impl/vector/__pycache__/local_hnsw.cpython-311.pyc,,
chromadb/segment/impl/vector/__pycache__/local_persistent_hnsw.cpython-311.pyc,,
chromadb/segment/impl/vector/batch.py,sha256=Zud9CslZ5G-KWnR6cEyIJU_CkyI4q87dsvM7eN2wYSg,4213
chromadb/segment/impl/vector/brute_force_index.py,sha256=F1lGpx5Fe77JOKGCNT4F4xfcUSJ5WWxaWncHFNznbPI,5420
chromadb/segment/impl/vector/hnsw_params.py,sha256=WsOgwglI8N7fYSLpTLEWLOXQhwpJsRgRD19unO4-yN4,3162
chromadb/segment/impl/vector/local_hnsw.py,sha256=516KV0bSBq8iJYTapgteFIM9P31tRl7-O18uXSZkkYY,12046
chromadb/segment/impl/vector/local_persistent_hnsw.py,sha256=K8E9DcvfYut-qGjXNEcOg3_JbLRGgkL67u7zyUYOVVc,22149
chromadb/serde.py,sha256=hOQDemy4xz3N5hYjk1Vv0WMCjxyAow9NWSu-vyJHbY0,1501
chromadb/server/__init__.py,sha256=KfAHxJipqapFZd2ijNH9-L9xQMDg8Q349xM8hY72hS8,172
chromadb/server/__pycache__/__init__.cpython-311.pyc,,
chromadb/server/fastapi/__init__.py,sha256=5ITqRusSzq2ynT9BoVafZDzWTOcNVwP75L4RvMAsWU0,70679
chromadb/server/fastapi/__pycache__/__init__.cpython-311.pyc,,
chromadb/server/fastapi/__pycache__/types.cpython-311.pyc,,
chromadb/server/fastapi/types.py,sha256=fWEB5WqTq4ZStO4gQJcisQaAxc_IBuu8GizJ4Yu55yY,2506
chromadb/telemetry/README.md,sha256=4P1G-ZE1OhJfyv5Wm3KW8NmGONLtUT10tQrj-in1aDc,587
chromadb/telemetry/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/telemetry/__pycache__/__init__.cpython-311.pyc,,
chromadb/telemetry/opentelemetry/__init__.py,sha256=4guEpnFGOysLE8WRw4JGT2CAE6zeBawkgDRI64Up2vQ,6021
chromadb/telemetry/opentelemetry/__pycache__/__init__.cpython-311.pyc,,
chromadb/telemetry/opentelemetry/__pycache__/fastapi.cpython-311.pyc,,
chromadb/telemetry/opentelemetry/__pycache__/grpc.cpython-311.pyc,,
chromadb/telemetry/opentelemetry/fastapi.py,sha256=-h-OzcgP8A9nKLCqJ4TPN-1KBE5p34wLB-NUYh1vKdg,405
chromadb/telemetry/opentelemetry/grpc.py,sha256=UcVKkdpanminmjrt__phICW-W71uw6BSdpkxuhlV9ZY,3731
chromadb/telemetry/product/__init__.py,sha256=ZP2MpTTYQ-njYtqGQFPrJm6D6qGWG0J84oe9LcScByo,2936
chromadb/telemetry/product/__pycache__/__init__.cpython-311.pyc,,
chromadb/telemetry/product/__pycache__/events.cpython-311.pyc,,
chromadb/telemetry/product/__pycache__/posthog.cpython-311.pyc,,
chromadb/telemetry/product/events.py,sha256=n4sMAyDGECXYhWKJPaRAhZjn7Rxt9QZP74KU7RvYJzQ,8665
chromadb/telemetry/product/posthog.py,sha256=l_3WELha54vnnJeyUQAM_SnrgNAQl8nbjCxxeK2XYwg,2176
chromadb/test/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/test/__pycache__/__init__.cpython-311.pyc,,
chromadb/test/__pycache__/conftest.cpython-311.pyc,,
chromadb/test/__pycache__/test_api.cpython-311.pyc,,
chromadb/test/__pycache__/test_chroma.cpython-311.pyc,,
chromadb/test/__pycache__/test_cli.cpython-311.pyc,,
chromadb/test/__pycache__/test_client.cpython-311.pyc,,
chromadb/test/__pycache__/test_config.cpython-311.pyc,,
chromadb/test/__pycache__/test_multithreaded.cpython-311.pyc,,
chromadb/test/api/__pycache__/test_collection.cpython-311.pyc,,
chromadb/test/api/__pycache__/test_delete_database.cpython-311.pyc,,
chromadb/test/api/__pycache__/test_get_database.cpython-311.pyc,,
chromadb/test/api/__pycache__/test_invalid_update.cpython-311.pyc,,
chromadb/test/api/__pycache__/test_limit_offset.cpython-311.pyc,,
chromadb/test/api/__pycache__/test_list_databases.cpython-311.pyc,,
chromadb/test/api/__pycache__/test_numpy_list_inputs.cpython-311.pyc,,
chromadb/test/api/__pycache__/test_types.cpython-311.pyc,,
chromadb/test/api/test_collection.py,sha256=A1_EmFeQggYba6X8aHnb-4rRiXvKrcJle2r5gp2SCGM,2191
chromadb/test/api/test_delete_database.py,sha256=Mxzs5O87nMBG8mC4FF-KsWa8UmudXcdQd9-1t--Kxas,2671
chromadb/test/api/test_get_database.py,sha256=qryIErWRY-1vgJ0TwMg7S09wIDP_ya7XsM1KdcK02-E,290
chromadb/test/api/test_invalid_update.py,sha256=ktsq4Nf68jUpNWbWJQXCnsgP5XdAXl-SnVoUzflQwTM,588
chromadb/test/api/test_limit_offset.py,sha256=stbPyPiBPvqnLSb_1CosxC3LKNq8RNpS4rdM_918izg,2025
chromadb/test/api/test_list_databases.py,sha256=JF35YrwOqgyIlAp7mBPM-i3elSL2-i9NJyG-ACFlezc,3108
chromadb/test/api/test_numpy_list_inputs.py,sha256=Sb5xADICNamUHLhl43fRN3uYqF46FhVPI4Z5cqTuAb8,2085
chromadb/test/api/test_types.py,sha256=8KywPUsmP1KqNySopje1Mv2CshtSwAiC8bkHJ31CxP8,3219
chromadb/test/auth/__pycache__/test_auth_utils.cpython-311.pyc,,
chromadb/test/auth/test_auth_utils.py,sha256=AbUZ9yHIVxqvagjnryj4vMKtOZ0jZf2fYshwiF7nFBQ,3039
chromadb/test/client/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/test/client/__pycache__/__init__.cpython-311.pyc,,
chromadb/test/client/__pycache__/create_http_client_with_basic_auth.cpython-311.pyc,,
chromadb/test/client/__pycache__/test_cloud_client.cpython-311.pyc,,
chromadb/test/client/__pycache__/test_create_http_client.cpython-311.pyc,,
chromadb/test/client/__pycache__/test_database_tenant.cpython-311.pyc,,
chromadb/test/client/__pycache__/test_database_tenant_auth.cpython-311.pyc,,
chromadb/test/client/__pycache__/test_multiple_clients_concurrency.cpython-311.pyc,,
chromadb/test/client/create_http_client_with_basic_auth.py,sha256=kh8c5JX6MhoK8k0-EtVbfp9l_zrG_g699GndjYj52dk,786
chromadb/test/client/test_cloud_client.py,sha256=WBUaiXUolIQ2lHrkOG2vG8LCIjHg0JcKfMpQdvaphKc,3083
chromadb/test/client/test_create_http_client.py,sha256=9o-0eMxs9sGtGWpIss7B0TlfOCy8r6-xYy2j8D4EtZM,557
chromadb/test/client/test_database_tenant.py,sha256=6lw55gBXnO85iX3aJ0fUd_gFn7yqTb-9Ixmznp0TuhY,7171
chromadb/test/client/test_database_tenant_auth.py,sha256=UY6PS20mYjoRR_2PD5OvGk0DSOJv3XLEsAM4Yh7BEKE,3170
chromadb/test/client/test_multiple_clients_concurrency.py,sha256=dIGmJal7dGgQIll96r7DxzvqoxjyeSY4cg2bJ8k4IXg,2006
chromadb/test/configurations/__pycache__/test_collection_configuration.cpython-311.pyc,,
chromadb/test/configurations/__pycache__/test_configurations.cpython-311.pyc,,
chromadb/test/configurations/test_collection_configuration.py,sha256=2iKrBnjuBeNNLOYjJqVrh9Sp8lsLdrV900OAE7pzPuA,43762
chromadb/test/configurations/test_configurations.py,sha256=Ge-cZc09WSQN2FN6oBh4Yu-MNSZ20NbgZjTjelKc6nQ,3680
chromadb/test/conftest.py,sha256=MkygNuMTQveWA2nYHxBT0tILu6rzxKf_cFVelRyX-6c,35949
chromadb/test/data_loader/__pycache__/test_data_loader.cpython-311.pyc,,
chromadb/test/data_loader/test_data_loader.py,sha256=gs3ieDw1Li0__m7K548hTwxDMb0M3VuI1uKyNLPfZU0,3708
chromadb/test/db/__pycache__/test_log_purge.cpython-311.pyc,,
chromadb/test/db/test_log_purge.py,sha256=f4w8PU1ATbv-AKN6mlEbdHpgaQwtF8CUAp2ktRuU5sQ,1773
chromadb/test/distributed/README.md,sha256=E_RdZx4Jt1fp-V72CnZ5lB5AQLe_sTR-53AKuUFyxNQ,233
chromadb/test/distributed/__pycache__/test_log_failover.cpython-311.pyc,,
chromadb/test/distributed/__pycache__/test_reroute.cpython-311.pyc,,
chromadb/test/distributed/__pycache__/test_sanity.cpython-311.pyc,,
chromadb/test/distributed/test_log_failover.py,sha256=8za1C9-3Iq2Qe5lGAaQRYnnavKg3S4cP4gzGfTvtS1Q,10508
chromadb/test/distributed/test_reroute.py,sha256=tHiycpNSHnr1co895zWgKsk2x_yXWy5BVXkn_8_Vgcw,2442
chromadb/test/distributed/test_sanity.py,sha256=pV9TVCDj8WbnZdBR8YL9Vm2mPZl5On_vprLLBkBz2QA,2882
chromadb/test/ef/__pycache__/test_custom_ef.cpython-311.pyc,,
chromadb/test/ef/__pycache__/test_default_ef.cpython-311.pyc,,
chromadb/test/ef/__pycache__/test_ef.cpython-311.pyc,,
chromadb/test/ef/__pycache__/test_multimodal_ef.cpython-311.pyc,,
chromadb/test/ef/__pycache__/test_ollama_ef.cpython-311.pyc,,
chromadb/test/ef/__pycache__/test_onnx_mini_lm_l6_v2.cpython-311.pyc,,
chromadb/test/ef/__pycache__/test_openai_ef.cpython-311.pyc,,
chromadb/test/ef/__pycache__/test_voyageai_ef.cpython-311.pyc,,
chromadb/test/ef/test_custom_ef.py,sha256=sB3qzqgUqMKjkaCBUWIZiSGThkjyojcpNjHMSbTYSok,3303
chromadb/test/ef/test_default_ef.py,sha256=fhs1NE2gTcF9zLXNDbLoBwVa9dzSLN4H9pJAsHjjhn0,2660
chromadb/test/ef/test_ef.py,sha256=QNAfXWk9H6NUs2jlYvCg38iFsYw1khzqAxLwrZ603aM,3396
chromadb/test/ef/test_multimodal_ef.py,sha256=pJi-QevJQfZxnutHx9Fp0aDq9nR4QDr8snqNQ9Po8xc,6174
chromadb/test/ef/test_ollama_ef.py,sha256=SIxBSMnK-ubdt1Um1iXwSojajR3wEtfyMpuUEwV68e4,1835
chromadb/test/ef/test_onnx_mini_lm_l6_v2.py,sha256=QSguSx3H80VkCWwBn2X-QUUBMEr_kfe21B72KJVBYFs,7542
chromadb/test/ef/test_openai_ef.py,sha256=lEjKfM4hsrOhTDLnk-OisCnP0LlAGuKhO7hG_4D38sw,1246
chromadb/test/ef/test_voyageai_ef.py,sha256=DEZRP-OTTcFtEwcd5fvAXOcDbNKWiPcMGsUPvEL9CrY,603
chromadb/test/openssl.cnf,sha256=QBm_dDan5xjTJ5f4oZhCr7xmvmBQT-30vmo7o2cors4,189
chromadb/test/property/__pycache__/invariants.cpython-311.pyc,,
chromadb/test/property/__pycache__/strategies.cpython-311.pyc,,
chromadb/test/property/__pycache__/test_add.cpython-311.pyc,,
chromadb/test/property/__pycache__/test_client_url.cpython-311.pyc,,
chromadb/test/property/__pycache__/test_collections.cpython-311.pyc,,
chromadb/test/property/__pycache__/test_collections_with_database_tenant.cpython-311.pyc,,
chromadb/test/property/__pycache__/test_collections_with_database_tenant_overwrite.cpython-311.pyc,,
chromadb/test/property/__pycache__/test_cross_version_persist.cpython-311.pyc,,
chromadb/test/property/__pycache__/test_embeddings.cpython-311.pyc,,
chromadb/test/property/__pycache__/test_filtering.cpython-311.pyc,,
chromadb/test/property/__pycache__/test_fork.cpython-311.pyc,,
chromadb/test/property/__pycache__/test_persist.cpython-311.pyc,,
chromadb/test/property/__pycache__/test_restart_persist.cpython-311.pyc,,
chromadb/test/property/invariants.py,sha256=UeCnhtYl7QvAznqDrv1rXg3UAPPyrTtRpSawj1W6gwo,22856
chromadb/test/property/strategies.py,sha256=s9g8RSHIIsKUlqyv4-5r9B0jC4e1Cw5oOccuiCP1mlw,24584
chromadb/test/property/test_add.py,sha256=TIHtabgwlIKdJYQKRT3t2xiRuiiYIaa2kQo0S2zUzDo,10940
chromadb/test/property/test_client_url.py,sha256=PjT9vHBnSawQ9J2VhmS1UDbZpAKMLo52Z3IzuSLNnR4,3881
chromadb/test/property/test_collections.py,sha256=ynhNTpu4F98PiyK3hhmAID_oUgYkhs0Qe1HcGgmCyQg,12327
chromadb/test/property/test_collections_with_database_tenant.py,sha256=xvXNWAJxkeeh3UmPog5cAvLzE2Ceu8gRA81HnXzd4-k,6047
chromadb/test/property/test_collections_with_database_tenant_overwrite.py,sha256=x7SAHwNzjmO4doDFgH1cCGEjM9sDSOCGwxqJqp9391I,7713
chromadb/test/property/test_cross_version_persist.py,sha256=Z-vr1wlFLAneEi1h3svoUzACL0kKpnjgFBzohRkJ-DI,13569
chromadb/test/property/test_embeddings.py,sha256=UyQl-HvAwC41hl0kKD-_yuLD-QzmHWtevKUrtLgTvdk,51997
chromadb/test/property/test_filtering.py,sha256=AVwBFyeYM-dnPCaiZ4QqEzxRunG_-lHY3iif3Qvyfuw,20056
chromadb/test/property/test_fork.py,sha256=GmNOs6mg-32NR4z20Qt2NuUEWyu778SrAWIDvhCAOE4,7831
chromadb/test/property/test_persist.py,sha256=pIVIB1_epfzOiiYcUv0o_DB1VByqsVUzim84Iui0BjY,23573
chromadb/test/property/test_restart_persist.py,sha256=UnUt3-VyHO83tGbv_DgeEHgxHp6Pf4AbXC5BP-pp-do,3347
chromadb/test/segment/distributed/__pycache__/test_memberlist_provider.cpython-311.pyc,,
chromadb/test/segment/distributed/__pycache__/test_rendezvous_hash.cpython-311.pyc,,
chromadb/test/segment/distributed/test_memberlist_provider.py,sha256=dkqWKygjPjV28IR04EkPNMex-9Hvvn6XWHMaOonhHzM,3836
chromadb/test/segment/distributed/test_rendezvous_hash.py,sha256=7OyO0j5wZrrJWMZllcYx0S1UyHJt8s3kpXwAMqoK1Z0,2253
chromadb/test/stress/__pycache__/test_many_collections.cpython-311.pyc,,
chromadb/test/stress/test_many_collections.py,sha256=WzqNhxxPvNUKUVEoajRL7DiYtxitFG88AMEljx0sKzc,1117
chromadb/test/test_api.py,sha256=rCjuStmR-rOe65QgskXqHEp8pw-F403b2lXQPcH2bXM,55014
chromadb/test/test_chroma.py,sha256=YMYfxAi_hHGwdocUsLENzuCFkuApE0mla54aZPgMIY8,4329
chromadb/test/test_cli.py,sha256=ah5vHiyeTQ1NVy4-XnYAFSX4i040JU5Rd-KZIhaBL1Y,5049
chromadb/test/test_client.py,sha256=AtuUqjeHkPruTzwsBGmTM7V0Pd91WC9Uh_kNXiaQEM4,3563
chromadb/test/test_config.py,sha256=_GpZzYfXPagtWf3sV9aHBKbI0gedzj_xnewMzA2Uxas,4127
chromadb/test/test_multithreaded.py,sha256=801L6_odKornoJuP7NXl9RP4R70i1FzslJBSqZn5B0w,8545
chromadb/test/utils/__pycache__/cross_version.cpython-311.pyc,,
chromadb/test/utils/__pycache__/distance_functions.cpython-311.pyc,,
chromadb/test/utils/__pycache__/test_embedding_function_schemas.cpython-311.pyc,,
chromadb/test/utils/__pycache__/test_result_df_transform.cpython-311.pyc,,
chromadb/test/utils/__pycache__/wait_for_version_increase.cpython-311.pyc,,
chromadb/test/utils/cross_version.py,sha256=y1ackS4ZLmOXjJj5VxvUPL1GW-xXJBZSXeErDRToBjw,2254
chromadb/test/utils/distance_functions.py,sha256=4IEBBXrprW-uHhNOiO2yzMvVO4SIaZACTxC6JWXRJUM,184
chromadb/test/utils/test_embedding_function_schemas.py,sha256=qsEwxms5g4RtIa0M5dSumpimzCIn3EGaI1lrNLVE474,4627
chromadb/test/utils/test_result_df_transform.py,sha256=7Ife7sBwlTXe9NwY-ThHgSsogtj74MHUmUAht6S0pzk,5946
chromadb/test/utils/wait_for_version_increase.py,sha256=fPd_8slKAh1jFe8qlhpZMWtEblW_4Bq0-umEtkgU86Q,873
chromadb/types.py,sha256=6O0KLvF9AsPB8YG-jI3R3VWp2gzTlnnRvsZiFXsdqtQ,9186
chromadb/utils/__init__.py,sha256=-t25WU4oRKXU3I4PF8G_uVM2UNzu1SfjfS1DL2P0WVQ,378
chromadb/utils/__pycache__/__init__.cpython-311.pyc,,
chromadb/utils/__pycache__/async_to_sync.cpython-311.pyc,,
chromadb/utils/__pycache__/batch_utils.cpython-311.pyc,,
chromadb/utils/__pycache__/data_loaders.cpython-311.pyc,,
chromadb/utils/__pycache__/delete_file.cpython-311.pyc,,
chromadb/utils/__pycache__/directory.cpython-311.pyc,,
chromadb/utils/__pycache__/distance_functions.cpython-311.pyc,,
chromadb/utils/__pycache__/fastapi.cpython-311.pyc,,
chromadb/utils/__pycache__/lru_cache.cpython-311.pyc,,
chromadb/utils/__pycache__/messageid.cpython-311.pyc,,
chromadb/utils/__pycache__/read_write_lock.cpython-311.pyc,,
chromadb/utils/__pycache__/rendezvous_hash.cpython-311.pyc,,
chromadb/utils/__pycache__/results.cpython-311.pyc,,
chromadb/utils/async_to_sync.py,sha256=NLFs4lFaJuhrU_XEQEFDumdYn_EzZ4_WKAhwiOO3chg,1678
chromadb/utils/batch_utils.py,sha256=Sgc4XMx1wuytK2O8_0xwAbLMAkrgfuUF3kSDk1nTeQQ,1239
chromadb/utils/data_loaders.py,sha256=U-dAoLiphHm6FZTAwUTfP-7yLeH8TGLJNIWQweuqLqg,1387
chromadb/utils/delete_file.py,sha256=FUqldkeqFQE5O2S-aH4zEb27JwZTQ2_PK7sqMRHpN74,1130
chromadb/utils/directory.py,sha256=BxM3RomtO1-ZK50kWMaNJBUgTulgEYNcYC7YaDE72gA,622
chromadb/utils/distance_functions.py,sha256=qFFTLBLTMDi_utMrlp07fQi0SxFmtrnqBOg0TfADEY4,957
chromadb/utils/embedding_functions/__init__.py,sha256=fDMApqa2jhAB3VMS1w7y5CWlz9hNQx44wtlWLo1XNzg,8094
chromadb/utils/embedding_functions/__pycache__/__init__.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/amazon_bedrock_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/baseten_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/chroma_langchain_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/cloudflare_workers_ai_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/cohere_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/google_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/huggingface_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/instructor_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/jina_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/mistral_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/ollama_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/onnx_mini_lm_l6_v2.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/open_clip_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/openai_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/roboflow_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/sentence_transformer_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/text2vec_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/together_ai_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/voyageai_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/amazon_bedrock_embedding_function.py,sha256=qalzT-jUDnrEauExsVlQAx21tWPz1QYdtTSW_1HthkE,4787
chromadb/utils/embedding_functions/baseten_embedding_function.py,sha256=GWK52Lb8BzLZVHnhlkqJGhquvgpOVM_W-JEI0bV22pY,3532
chromadb/utils/embedding_functions/chroma_langchain_embedding_function.py,sha256=jpgvIm-auQok06uDzNpHtUQ5flPV4QhLEpP_hFkhmWQ,6158
chromadb/utils/embedding_functions/cloudflare_workers_ai_embedding_function.py,sha256=GSNWYVISAIYm0VIsxsHeIBoo293uGISSiX2J6tiMgTY,5227
chromadb/utils/embedding_functions/cohere_embedding_function.py,sha256=zSozeKOwce9Y668q-DweRkYKN8D-H7-ganFOR9to5Kg,6500
chromadb/utils/embedding_functions/google_embedding_function.py,sha256=XHtk71vTyETRKkmqaoyvq4zF6IHcFRkooDrDiBbwQak,14335
chromadb/utils/embedding_functions/huggingface_embedding_function.py,sha256=7VfXuq0A7LC4_OPKWMhXZg6YKdrAGToKwwxNvqw7ETk,8402
chromadb/utils/embedding_functions/instructor_embedding_function.py,sha256=ACwbdQNn7WzhMc8_z1SgYrnZ3k5CCnhfBGEAVhdjhyE,4244
chromadb/utils/embedding_functions/jina_embedding_function.py,sha256=ABjzhEB9teqeSePNQLQGJHbAEyr1UwdBZAyuVd5XdD0,7498
chromadb/utils/embedding_functions/mistral_embedding_function.py,sha256=f1rnx-KhIPkwPuS0OVwTCsa3Z8albIgwRw1lI9Oiox8,3123
chromadb/utils/embedding_functions/ollama_embedding_function.py,sha256=zt3pygV4N5oN2ioGiG08Q16ej2Cdiwjg9qpmb91tUCI,4027
chromadb/utils/embedding_functions/onnx_mini_lm_l6_v2.py,sha256=485IH5tpPut0jhboZyozWg0FMLBKodwPQlBBJ7nbUYE,13571
chromadb/utils/embedding_functions/open_clip_embedding_function.py,sha256=Q_YmBDqvpwZKmEuj2g7LVOviY46jAyPqV7L7xNnIbic,6070
chromadb/utils/embedding_functions/openai_embedding_function.py,sha256=NR6Bfm2wsrbld9X0y3B5QNihgBTN7wKdVqTvFcg9njQ,8221
chromadb/utils/embedding_functions/roboflow_embedding_function.py,sha256=zFKqghn9P3XB5nvu6jpCg8F-FawmSr4c5CGnFWG_z3A,5015
chromadb/utils/embedding_functions/schemas/README.md,sha256=cAcdrzPhuGaFs-70z8Uwmtw9QBhVwPQRpmWzyYoLi3I,1659
chromadb/utils/embedding_functions/schemas/__init__.py,sha256=3XAKDuCRcogo3W-QlTDIYmUak-VN2NWLj_ue6hzLC4g,469
chromadb/utils/embedding_functions/schemas/__pycache__/__init__.cpython-311.pyc,,
chromadb/utils/embedding_functions/schemas/__pycache__/registry.cpython-311.pyc,,
chromadb/utils/embedding_functions/schemas/__pycache__/schema_utils.cpython-311.pyc,,
chromadb/utils/embedding_functions/schemas/registry.py,sha256=F542ce15GYAAzrb9rmuEYkypDEc1LKFahPgmzKk39hI,1599
chromadb/utils/embedding_functions/schemas/schema_utils.py,sha256=ybSD5s7M6UgZ0PA5cYbj4gvJcFXDaU98HLuRZhYJdkg,2590
chromadb/utils/embedding_functions/sentence_transformer_embedding_function.py,sha256=K3xK3K6y2LkRIzIvNhAXfKQlY9A6AP9cpeU8_bd3ifk,4579
chromadb/utils/embedding_functions/text2vec_embedding_function.py,sha256=B6lxVQ_UbngvtsIQkO7sTWkgZRdaUF7uUcygkkBEfSM,3081
chromadb/utils/embedding_functions/together_ai_embedding_function.py,sha256=S9MxltPu8ICIxN9-GExgf7mB4PsOtvh1U_OEwYdeGuc,4460
chromadb/utils/embedding_functions/voyageai_embedding_function.py,sha256=JcaYw11kubV9mkN6-9rYdfgUEhI-SkExW89Ef6skfbQ,3904
chromadb/utils/fastapi.py,sha256=LIYvUjwBVxWsXavC50wLnkipp-5_Owolwp_f6MvKccQ,504
chromadb/utils/lru_cache.py,sha256=7szY8FsguzNDD5eXU0RmJjxmAxXAYCag77lv0pWagdQ,1076
chromadb/utils/messageid.py,sha256=6utlGTj1HjbRFKBN97ydUmekYx3_l62Vv37ijkKP8OU,272
chromadb/utils/read_write_lock.py,sha256=AxZ7GdYC9g05ZLmzLKTw051_EbvEQ8F9CIYpONcYDbI,2010
chromadb/utils/rendezvous_hash.py,sha256=6rhpTVeBBjZ9RwfedH2qmm3tcDbkEYT_owxd64AbB-Q,2155
chromadb/utils/results.py,sha256=0XNZkRflId0C_En5dgZgFalKPCCdr2B_MN-7poWn5uY,3869
chromadb_rust_bindings/__init__.py,sha256=EQHYniO5ZjnemXFkmB-faa2gCtR8HIke29q6_jaFtkg,171
chromadb_rust_bindings/__pycache__/__init__.cpython-311.pyc,,
chromadb_rust_bindings/chromadb_rust_bindings.abi3.so,sha256=5qNQWFXzMG82AoDe6t_J5OrwJZMMlXrgR5H1CdRzRfY,45534192
schemas/embedding_functions/README.md,sha256=gD-XaVRks1tA8LnBVDS_n6-8w5lJrXbXBKeISsCvVVE,1969
schemas/embedding_functions/amazon_bedrock.json,sha256=JBoj8yT4ThiF9pDdSZKUEKQFjZbHflgYuuv_ZSgSPPg,731
schemas/embedding_functions/base_schema.json,sha256=LscBIjh5l2lT84dCUGqI9rx1X9gbjJseJVu6ZNHMXW0,734
schemas/embedding_functions/chroma_langchain.json,sha256=2vRSiEyMQZj11vKb65HV2pzsbRuvU7tLSTAm10ieC4s,478
schemas/embedding_functions/cloudflare_workers_ai.json,sha256=KUqyIGedKKDzsc5rkkyj_DarlTghFjk5otzxQeQdMMI,1063
schemas/embedding_functions/cohere.json,sha256=I4w2Z39kYxmpWw1arx6qcM2r7HpyipNczNxAI2KXkYA,681
schemas/embedding_functions/default.json,sha256=JgWxX_1YdQpwloBjU7rpaUmnuu0RjkYbkaxI-jb7h0Y,296
schemas/embedding_functions/google_generative_ai.json,sha256=1jHZ7LDckStF0AZmxR5rfINShWx8EEOT1ATug9PWMwo,805
schemas/embedding_functions/google_palm.json,sha256=8OaTgiRaZS8cFIOiXpRMrghN4iwIQ-x4rcKwwPi3pKs,628
schemas/embedding_functions/google_vertex.json,sha256=0ZjQ0NlT3rvQY0Bf_l90cykzDNLc_lR6n2AsZilVoeo,862
schemas/embedding_functions/huggingface.json,sha256=onqjGbDGrkrlHkLnesvIjkMMO2UJ_R1QHtoW2Rl4AuU,696
schemas/embedding_functions/huggingface_server.json,sha256=DSLE8JaJMu8l8ta7nhPKcZb2cU5tTfxefq4nTwLZq2Q,650
schemas/embedding_functions/instructor.json,sha256=2XRKfqo2kAyykNmPsyH4CPahGZ6oixKRCib-4T7beSw,731
schemas/embedding_functions/jina.json,sha256=kmd1J_WnylnxpnojLSSmbzsvcM1h4M-LLB4-FyBivFw,1363
schemas/embedding_functions/ollama.json,sha256=85V4ja3V3-MdtLSnM7vrnRQlpTu3oba2IAykwSFD9Vo,652
schemas/embedding_functions/onnx_mini_lm_l6_v2.json,sha256=xuD_AUgyxrmMJveKLvDQ0tAxw3767klbwrGHFhMvcCQ,529
schemas/embedding_functions/open_clip.json,sha256=CsCFoZyivRFVnI6YuJ3nYZc1OeQvkDJz3_8GQnHCnp0,742
schemas/embedding_functions/openai.json,sha256=SN8rkJOkc49e-ewAbBxXk-0UjkBD_6nsx1qCpx-x-AY,2005
schemas/embedding_functions/roboflow.json,sha256=jG5xbdmd7Hr-b8uEW7WnDFaaUYeE0Xgbc4z6HMetUFs,606
schemas/embedding_functions/sentence_transformer.json,sha256=GQIuTaTcbvCwyonb0jKy5b09RpR6Lo9b8DAf1lW2SXE,1244
schemas/embedding_functions/text2vec.json,sha256=1qlkFeGirQeYdA5oQY9Y6b1xDPShiWFqlhJp2oW1A-Q,451
schemas/embedding_functions/together_ai.json,sha256=sJHcH_Q8UnvGkXueq3jDEtO2MBe_KrYSrHfqDgGyRXQ,700
schemas/embedding_functions/transformers.json,sha256=UUxaxVoOYwIYhkIhvPAvEUe9W7sYqNJvMGm6bW76OHA,836
schemas/embedding_functions/voyageai.json,sha256=XO9wB-GW6o2BXR2MutcCvgQSY9GK1QFg5DVm2WQ7nxk,615
